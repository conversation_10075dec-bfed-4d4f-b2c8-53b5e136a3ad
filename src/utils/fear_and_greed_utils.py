import requests
import json


def get_fear_and_greed_index() -> dict | None:
    url = "https://api.alternative.me/fng/?limit=1&format=json"
    try:
        response = requests.get(url)
        data = json.loads(response.text)
        return {
            "value": data["data"][0]["value"],
            "value_classification": data["data"][0]["value_classification"],
        }
    except Exception as e:
        print(f"Error al obtener el índice de miedo y codicia: {e}")
        return None
