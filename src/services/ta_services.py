from typing import override
import numpy as np
from utils.talib_wrapper import TA<PERSON>rapper
from services.binance_client import BinanceClient
import pandas as pd
from models.Indicators.MarketIndicators import MarketIndicators
from utils.fear_and_greed_utils import get_fear_and_greed_index


class TAService:
    def __init__(self, binance_client: BinanceClient):
        self.binance_client = binance_client
        self.rsi = None
        self.sma = None
        self.ema = None
        self.bbands = {"upper": None, "middle": None, "lower": None}
        self.atr = None
        self.sar = None
        self.fear_and_greed = None
        self.trading_pair = None

        self.market_indicators = MarketIndicators()

    def _get_indicators_value(self):
        try:
            # Fetch historical data
            data = self.binance_client.get_historical_data()
            if data.empty:
                print("Data is empty")
                return None

            # Initialize TA-Lib wrapper
            ta = TAWrapper(data)
            # Calculate indicators
            self.trading_pair = data["symbol"].iloc[0]
            sma = ta.SMA(timeperiod=200, price="close")
            self.sma = (
                round(float(sma.iloc[-1]), 2)
                if not sma.empty and not pd.isna(sma.iloc[-1])
                else None
            )
            # Calculate EMA
            ema = ta.EMA(timeperiod=200, price="close")
            self.ema = (
                float(ema.iloc[-1])
                if not ema.empty and not pd.isna(ema.iloc[-1])
                else None
            )
            # Calculate RSI
            rsi = ta.RSI(timeperiod=14, price="close")
            self.rsi = (
                round(float(rsi.iloc[-1]), 2)
                if not rsi.empty and not pd.isna(rsi.iloc[-1])
                else None
            )
            # Calculate ATR
            atr = ta.ATR(timeperiod=14, price="close")
            self.atr = (
                round(float(atr.iloc[-1]), 2)
                if not atr.empty and not pd.isna(atr.iloc[-1])
                else None
            )
            # Calculate SAR
            sar = ta.SAR(acceleration=0.02, maximum=0.2, price="close")
            self.sar = (
                round(float(sar.iloc[-1]), 2)
                if not sar.empty and not pd.isna(sar.iloc[-1])
                else None
            )
            # Calculate BBANDS
            bbands_result = ta.BBANDS(
                timeperiod=20, nbdevup=2.0, nbdevdn=2.0, price="close"
            )
            upper = bbands_result["upperband"]
            self.bbands["upper"] = (
                round(float(upper.iloc[-1]), 2)
                if not upper.empty and not pd.isna(upper.iloc[-1])
                else None
            )
            middle = bbands_result["middleband"]
            self.bbands["middle"] = (
                round(float(middle.iloc[-1]), 2)
                if not middle.empty and not pd.isna(middle.iloc[-1])
                else None
            )
            lowest = bbands_result["lowerband"]
            self.bbands["lower"] = (
                round(float(lowest.iloc[-1]), 2)
                if not lowest.empty and not pd.isna(lowest.iloc[-1])
                else None
            )
            # Calculate AVGPRICE
            avg_price = ta.AVGPRICE()
            self.avg_price = (
                round(float(avg_price.iloc[-1]), 2)
                if not avg_price.empty and not pd.isna(avg_price.iloc[-1])
                else None
            )
            # Calculate MEDPRICE
            med_price = ta.MEDPRICE()
            self.median_price = (
                round(float(med_price.iloc[-1]), 2)
                if not med_price.empty and not pd.isna(med_price.iloc[-1])
                else None
            )
            # Calculate current price
            current_price = data["close"].iloc[-1]
            self.current_price = (
                round(float(current_price), 2) if not pd.isna(current_price) else None
            )

        except Exception as e:
            print(f"Error getting indicators: {e}")
            return None
    

    def get_market_indicators(self):
        """
        Fetch and return the latest market indicators such as RSI, EMA, SMA,
        Bollinger Bands, ATR, SAR, average price, median price, current price,
        and Fear & Greed index.
        """
        fear_and_greed = get_fear_and_greed_index()
        self._get_indicators_value()
        self.market_indicators.fear_and_greed.value = (
            int(fear_and_greed["value"]) if fear_and_greed else None
        )
        self.market_indicators.fear_and_greed.value_classification = (
            fear_and_greed["value_classification"] if fear_and_greed else None
        )
        self.market_indicators.trading_pair = self.trading_pair
        self.market_indicators.overlap_studies.ema = self.ema
        self.market_indicators.overlap_studies.sma = self.sma
        self.market_indicators.overlap_studies.bbands = self.bbands
        self.market_indicators.overlap_studies.sar = self.sar
        self.market_indicators.momentum_indicators.rsi = self.rsi
        self.market_indicators.volatility_indicators.atr = self.atr
        self.market_indicators.price_indicators.avg_price = self.avg_price
        self.market_indicators.price_indicators.median_price = self.median_price
        self.market_indicators.price_indicators.current_price = self.current_price

        print(self.market_indicators.model_dump())

        return self.market_indicators
        
