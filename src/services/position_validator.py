"""
Position Validator Module
Handles validation and synchronization of bot positions with Binance reality.
"""

import logging
import time
from binance.enums import SIDE_BUY, SIDE_SELL


class PositionValidator:
    """Validates and synchronizes position state with Binance."""

    def __init__(self, binance_client, state_manager, config):
        self.binance_client = binance_client
        self.state_manager = state_manager
        self.config = config
        self.logger = logging.getLogger(__name__)

    def validate_position_integrity(self, position):
        """Comprehensive validation of position integrity."""
        try:
            self.logger.info("Starting comprehensive position validation...")

            # Step 1: Validate order IDs exist and are filled
            if not self._validate_order_history(position):
                return False

            # Step 2: Validate balance matches expected position
            if not self._validate_balance_consistency(position):
                return False

            # Step 3: Validate position data consistency
            if not self._validate_position_data(position):
                return False

            self.logger.info("Position validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error during position validation: {e}")
            return False

    def _validate_order_history(self, position):
        """Validate all orders in position history."""
        try:
            # Validate initial order
            initial_order_id = position.get("order_id")
            if not initial_order_id:
                self.logger.error("Position missing initial order ID")
                return False

            validation = self.binance_client.validate_order_execution(
                initial_order_id, SIDE_BUY
            )

            if not validation["valid"]:
                self.logger.error(
                    f"Initial order {initial_order_id} validation failed: {validation['reason']}"
                )
                return False

            # Validate DCA orders
            for i, dca_order in enumerate(position.get("dca_orders", [])):
                dca_order_id = dca_order.get("order_id")
                if dca_order_id:
                    dca_validation = self.binance_client.validate_order_execution(
                        dca_order_id, SIDE_BUY
                    )
                    if not dca_validation["valid"]:
                        self.logger.error(
                            f"DCA order {i + 1} ({dca_order_id}) validation failed: {dca_validation['reason']}"
                        )
                        return False

            self.logger.debug("Order history validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Error validating order history: {e}")
            return False

    def _validate_balance_consistency(self, position):
        """Validate that our recorded position matches Binance balance."""
        try:
            base_asset = self.config["trading_pair"].replace("USDT", "")
            balance_info = self.binance_client.verify_position_exists(base_asset)

            if not balance_info:
                self.logger.error(
                    f"Could not retrieve {base_asset} balance from Binance"
                )
                return False

            expected_quantity = position["total_base_quantity"]
            actual_quantity = balance_info["total"]

            # Allow for small discrepancies due to fees (1% tolerance)
            tolerance = max(expected_quantity * 0.01, 0.00000001)

            if abs(actual_quantity - expected_quantity) > tolerance:
                self.logger.warning(f"Balance mismatch detected:")
                self.logger.warning(f"  Expected: {expected_quantity:.8f} {base_asset}")
                self.logger.warning(f"  Actual: {actual_quantity:.8f} {base_asset}")
                self.logger.warning(
                    f"  Difference: {abs(actual_quantity - expected_quantity):.8f}"
                )

                # If we have less than expected, it might have been sold externally
                if actual_quantity < expected_quantity * 0.5:
                    self.logger.error(
                        "Significant balance deficit detected. Position may have been closed externally."
                    )
                    return False

                # Update position with actual balance
                self._update_position_with_actual_balance(position, actual_quantity)

            self.logger.debug("Balance consistency validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Error validating balance consistency: {e}")
            return False

    def _validate_position_data(self, position):
        """Validate internal position data consistency."""
        try:
            # Check required fields
            required_fields = [
                "average_price",
                "total_base_quantity",
                "total_quote_spent",
                "take_profit_price",
            ]
            for field in required_fields:
                if field not in position or position[field] <= 0:
                    self.logger.error(f"Position missing or invalid field: {field}")
                    return False

            # Validate calculations
            calculated_avg_price = (
                position["total_quote_spent"] / position["total_base_quantity"]
            )
            recorded_avg_price = position["average_price"]

            if abs(calculated_avg_price - recorded_avg_price) > 0.01:
                self.logger.warning(f"Average price calculation mismatch:")
                self.logger.warning(f"  Calculated: {calculated_avg_price:.2f}")
                self.logger.warning(f"  Recorded: {recorded_avg_price:.2f}")
                # Fix the average price
                position["average_price"] = calculated_avg_price
                position["take_profit_price"] = calculated_avg_price * (
                    1 + position["take_profit_percentage"] / 100
                )
                self.state_manager.set_position(position)
                self.logger.info("Fixed average price calculation")

            self.logger.debug("Position data validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Error validating position data: {e}")
            return False

    def _update_position_with_actual_balance(self, position, actual_balance):
        """Update position data with actual balance from Binance."""
        try:
            old_quantity = position["total_base_quantity"]
            position["total_base_quantity"] = actual_balance

            # Recalculate average price if we have total spent
            if position.get("total_quote_spent", 0) > 0 and actual_balance > 0:
                position["average_price"] = (
                    position["total_quote_spent"] / actual_balance
                )
                position["take_profit_price"] = position["average_price"] * (
                    1 + position["take_profit_percentage"] / 100
                )

            self.state_manager.set_position(position)
            self.logger.info(
                f"Updated position quantity from {old_quantity:.8f} to {actual_balance:.8f}"
            )

        except Exception as e:
            self.logger.error(f"Error updating position with actual balance: {e}")

    def sync_position_with_binance(self, position):
        """Synchronize position with current Binance state."""
        try:
            self.logger.info("Synchronizing position with Binance...")

            summary = self.binance_client.get_position_summary()
            if not summary:
                self.logger.error("Could not get position summary from Binance")
                return False

            actual_base_balance = summary["base_balance"]["total"]

            if actual_base_balance <= 0:
                self.logger.warning(
                    "No base asset balance found. Position may have been closed externally."
                )
                return False

            # Update position with real data
            position["total_base_quantity"] = actual_base_balance

            # Recalculate average price if possible
            if position.get("total_quote_spent", 0) > 0:
                position["average_price"] = (
                    position["total_quote_spent"] / actual_base_balance
                )
                position["take_profit_price"] = position["average_price"] * (
                    1 + position["take_profit_percentage"] / 100
                )

            # Add sync timestamp
            position["last_sync_ts"] = time.time()

            self.state_manager.set_position(position)
            self.logger.info(
                f"Position synchronized - Quantity: {actual_base_balance:.8f}, Avg Price: {position['average_price']:.2f}"
            )

            return True

        except Exception as e:
            self.logger.error(f"Error synchronizing position: {e}")
            return False

    def log_position_status(self, position):
        """Log comprehensive position status for debugging."""
        try:
            base_asset = self.config["trading_pair"].replace("USDT", "")
            summary = self.binance_client.get_position_summary()

            self.logger.info("=" * 60)
            self.logger.info("POSITION STATUS REPORT")
            self.logger.info("=" * 60)

            # Local position data
            self.logger.info("LOCAL POSITION DATA:")
            self.logger.info(
                f"  Quantity: {position['total_base_quantity']:.8f} {base_asset}"
            )
            self.logger.info(f"  Average Price: {position['average_price']:.2f} USDT")
            self.logger.info(f"  Total Spent: {position['total_quote_spent']:.2f} USDT")
            self.logger.info(f"  Take Profit: {position['take_profit_price']:.2f} USDT")
            self.logger.info(f"  DCA Orders: {len(position.get('dca_orders', []))}")
            self.logger.info(f"  Initial Order ID: {position.get('order_id', 'N/A')}")
            self.logger.info(f"  Status: {position.get('status', 'N/A')}")

            # Binance reality
            if summary:
                self.logger.info("BINANCE REALITY:")
                self.logger.info(
                    f"  {base_asset} Balance: {summary['base_balance']['total']:.8f}"
                )
                self.logger.info(
                    f"  USDT Balance: {summary['usdt_balance']['total']:.2f}"
                )
                self.logger.info(
                    f"  Current Price: {summary['current_price']:.2f} USDT"
                )
                self.logger.info(
                    f"  Position Value: {summary['base_value_usdt']:.2f} USDT"
                )
                self.logger.info(
                    f"  Total Portfolio: {summary['total_value_usdt']:.2f} USDT"
                )

                # Calculate unrealized PnL
                if position["total_base_quantity"] > 0:
                    current_value = (
                        summary["base_balance"]["total"] * summary["current_price"]
                    )
                    unrealized_pnl = current_value - position["total_quote_spent"]
                    pnl_percentage = (
                        unrealized_pnl / position["total_quote_spent"]
                    ) * 100
                    self.logger.info(
                        f"  Unrealized PnL: {unrealized_pnl:.2f} USDT ({pnl_percentage:.2f}%)"
                    )

            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"Error logging position status: {e}")

    def recover_position_from_orders(self):
        """Attempt to recover position from recent order history."""
        try:
            self.logger.info("Attempting to recover position from order history...")

            # Get recent trades
            recent_trades = self.binance_client.get_recent_trades(50)
            if not recent_trades:
                self.logger.warning("No recent trades found")
                return None

            # Analyze trades to reconstruct position
            buy_trades = [t for t in recent_trades if t["isBuyer"]]
            sell_trades = [t for t in recent_trades if not t["isBuyer"]]

            if not buy_trades:
                self.logger.warning("No recent buy trades found")
                return None

            # Calculate position from buy trades
            total_qty = sum(float(t["qty"]) for t in buy_trades)
            total_spent = sum(float(t["quoteQty"]) for t in buy_trades)
            avg_price = total_spent / total_qty if total_qty > 0 else 0

            # Subtract any sells
            for sell in sell_trades:
                sell_qty = float(sell["qty"])
                sell_value = float(sell["quoteQty"])
                if sell_qty <= total_qty:
                    total_qty -= sell_qty
                    # Adjust total spent proportionally
                    total_spent -= (sell_qty / (total_qty + sell_qty)) * total_spent

            if total_qty <= 0:
                self.logger.info("No net position found from trade history")
                return None

            # Create recovered position
            recovered_position = {
                "entry_price": avg_price,
                "average_price": avg_price,
                "total_base_quantity": total_qty,
                "total_quote_spent": total_spent,
                "take_profit_percentage": self.config["take_profit_percentage"],
                "trailing_stop_deviation_percentage": self.config[
                    "trailing_stop_deviation_percentage"
                ],
                "take_profit_price": avg_price
                * (1 + self.config["take_profit_percentage"] / 100),
                "dca_orders": [],
                "trailing_stop_activated": False,
                "trailing_stop_peak_price": 0,
                "trailing_stop_price": 0,
                "status": "recovered",
                "last_ai_check_ts": time.time(),
                "recovery_timestamp": time.time(),
            }

            self.logger.info(
                f"Position recovered - Qty: {total_qty:.8f}, Avg Price: {avg_price:.2f}"
            )
            return recovered_position

        except Exception as e:
            self.logger.error(f"Error recovering position from orders: {e}")
            return None
