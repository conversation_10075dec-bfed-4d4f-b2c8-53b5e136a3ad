from langchain_tavily import <PERSON>lySearch
from langgraph.prebuilt import create_react_agent
from constants.openai_models import OpenAIModels

web_search = TavilySearch(max_results=3, search_depth="finance")
web_search_results = web_search.invoke(" What are the latest news for Ethereum OR crypto, what can impact in the market today?")
print(web_search_results)

def create_research_agent():
    research_agent = create_react_agent(
        model=OpenAIModels.GPT_5_NANO,
        tools=[web_search],
        prompt=(
            "You are a research agent.\n\n"
            "INSTRUCTIONS:\n"
            "- Assist ONLY with research-related tasks, DO NOT do any math\n"
            "- After you're done with your tasks, respond to the supervisor directly\n"
            "- Respond ONLY with the results of your work, do NOT include ANY other text."
        ),
        name="research_agent",
    )
