# This file holds all the user-configurable settings for the bot.
# It's loaded at startup and used throughout the application.

# Trading Parameters
trading_pair: "ETHUSDT"

# ==============================================================================
# >> Risk Management & Position Sizing <<
# ==============================================================================
risk_per_trade_percentage: 1.0        # Arriesgar el 1% del capital de USDT por operación.
atr_length: 14                        # Período para el cálculo del Average True Range (ATR).
atr_multiplier_for_stop_loss: 2.0     # Distancia del Stop-Loss inicial (2 veces el ATR).
# ==============================================================================
# EMA Settings
ema_length: 200
ema_timeframe: "4h"

# DCA Strategy Settings
initial_order_size_quote: 100.0       # Initial buy order size in USDT
dca_deviation_percentage: 2.0        # Price must drop by this % to trigger a DCA buy
dca_order_size_multiplier: 1.5       # Each subsequent DCA order is 1.5x the previous one
max_dca_orders: 5                    # Maximum number of DCA orders for a single position

# Profit and Stop-Loss Settings
take_profit_percentage: 3.0          # Target profit % from the average entry price
trailing_stop_activation_percentage: 1.0 # Price must rise this % above avg price to activate trailing stop
trailing_stop_deviation_percentage: 0.5 # Trigger sale if price drops by this % from its peak after activation

# AI Engine Settings
ai_enabled: true
ai_analysis_interval_minutes: 30     # How often to query the AI for market conditions

# Risk Management
max_portfolio_allocation_percentage: 50.0 # Never use more than 50% of the total quote currency balance
circuit_breaker_threshold_percentage: 15.0 # Halt trading if price drops by this % in a short time

# Kline Settings
kline_interval: "15m"                 # Interval for fetching historical data and WebSocket
kline_lookback: "100 hours ago UTC"   # How far back to look for historical data

# Operational Settings
check_interval_seconds: 10           # How often the main loop runs
log_file: "logs/bot_activity.log"
state_file: "config/bot_state.json"