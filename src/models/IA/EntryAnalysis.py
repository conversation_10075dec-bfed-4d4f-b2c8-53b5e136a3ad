# Define the desired JSON structure for the AI's response for a NEW entry
from pydantic import BaseModel, Field


class EntryAnalysis(BaseModel):
    market_sentiment: str = Field(
        description="Options: 'Very Bullish', 'Bullish', 'Neutral', 'Bearish', 'Very Bearish'"
    )
    volatility_outlook: str = Field(description="Options: 'High', 'Medium', 'Low'")
    confidence_score: float = Field(
        description="A float between 0.0 and 1.0 for entering a new trade."
    )
    reasoning: str = Field(
        description="A brief explanation for the recommended action."
    )