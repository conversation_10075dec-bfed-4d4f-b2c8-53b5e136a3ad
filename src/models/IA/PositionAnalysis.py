from pydantic import BaseModel, Field
from typing import Optional

class PositionAnalysis(BaseModel):
    action: str = Field(description="Recommended action. Options: 'hold', 'sell_now'")
    reasoning: str = Field(
        description="A brief explanation for the recommended action."
    )
    new_take_profit_percentage: Optional[float] = Field(
        None,
        description="Suggest a new take-profit percentage based on market conditions.",
    )
    new_trailing_stop_deviation_percentage: Optional[float] = Field(
        None, description="Suggest a new trailing stop deviation based on volatility."
    )