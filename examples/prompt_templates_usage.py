#!/usr/bin/env python3
"""
Example script demonstrating the new prompt templates architecture.

This script shows how to use the refactored prompt templates system
for better maintainability and extensibility.

Author: Senior Python Developer
"""

import sys
import os

# Add the src directory to the path to import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from constants.prompt_templates import (
    TradingPromptTemplates, 
    PromptType, 
    get_entry_analysis_prompt,
    get_position_analysis_prompt,
    get_startup_analysis_prompt
)


def demonstrate_template_usage():
    """Demonstrate how to use the new prompt templates architecture."""
    
    print("=== Trading Prompt Templates Architecture Demo ===\n")
    
    # 1. Show available templates
    print("1. Available Templates:")
    templates = TradingPromptTemplates.get_available_templates()
    for name, description in templates.items():
        print(f"   - {name}: {description}")
    print()
    
    # 2. Get specific templates using the class methods
    print("2. Getting Templates Using Class Methods:")
    
    entry_template = TradingPromptTemplates.get_entry_analysis_template()
    print(f"   Entry Analysis Template:")
    print(f"   - Description: {entry_template.description}")
    print(f"   - Required Variables: {len(entry_template.required_variables)} variables")
    print(f"   - Optional Variables: {len(entry_template.optional_variables)} variables")
    print()
    
    # 3. Get templates using the enum-based method
    print("3. Getting Templates Using Enum:")
    
    position_template = TradingPromptTemplates.get_template(PromptType.POSITION_ANALYSIS)
    print(f"   Position Analysis Template:")
    print(f"   - Description: {position_template.description}")
    print(f"   - Required Variables: {sorted(position_template.required_variables)}")
    print()
    
    # 4. Demonstrate template validation
    print("4. Template Validation:")
    
    # Example of valid data
    valid_data = {
        "trading_pair": "BTC/USDT",
        "current_price": "45000",
        "avg_price": "44500",
        "median_price": "44800",
        "ema": "44200",
        "sma": "44300",
        "sar": "43800",
        "bband_upper": "45500",
        "bband_middle": "44500",
        "bband_lower": "43500",
        "rsi": "65",
        "atr": "1200",
        "news_headlines": "Bitcoin shows strong momentum",
        "sentiment_score": "75 (Greed)",
        "format_instructions": "Return JSON format..."
    }
    
    try:
        is_valid = TradingPromptTemplates.validate_template_variables(
            entry_template, valid_data
        )
        print(f"   ✓ Valid data validation: {is_valid}")
    except ValueError as e:
        print(f"   ✗ Validation error: {e}")
    
    # Example of invalid data (missing required variables)
    invalid_data = {
        "trading_pair": "BTC/USDT",
        "current_price": "45000",
        # Missing many required variables...
    }
    
    try:
        TradingPromptTemplates.validate_template_variables(
            entry_template, invalid_data
        )
        print("   ✗ Invalid data should have failed validation")
    except ValueError as e:
        print(f"   ✓ Correctly caught validation error: Missing variables detected")
    print()
    
    # 5. Show convenience functions
    print("5. Convenience Functions:")
    
    entry_prompt_str = get_entry_analysis_prompt()
    print(f"   Entry prompt length: {len(entry_prompt_str)} characters")
    
    position_prompt_str = get_position_analysis_prompt()
    print(f"   Position prompt length: {len(position_prompt_str)} characters")
    
    startup_prompt_str = get_startup_analysis_prompt()
    print(f"   Startup prompt length: {len(startup_prompt_str)} characters")
    print()
    
    # 6. Show template structure
    print("6. Template Structure Example:")
    print("   Entry Analysis Template Preview:")
    preview = entry_template.template[:200] + "..." if len(entry_template.template) > 200 else entry_template.template
    print(f"   {preview}")
    print()
    
    print("=== Demo Complete ===")


def demonstrate_ai_engine_integration():
    """Show how the AI engine would use these templates."""
    
    print("\n=== AI Engine Integration Example ===\n")
    
    # This would be how the AI engine uses the templates
    print("Example of how AIEngine class uses the new architecture:")
    print("""
    class AIEngine:
        def __init__(self, config):
            # Initialize with new template system
            self.prompt_templates = TradingPromptTemplates()
            self.entry_prompt = self._create_prompt_from_template(PromptType.ENTRY_ANALYSIS)
            self.position_prompt = self._create_prompt_from_template(PromptType.POSITION_ANALYSIS)
        
        def _create_prompt_from_template(self, prompt_type: PromptType):
            template = self.prompt_templates.get_template(prompt_type)
            return ChatPromptTemplate.from_template(
                template=template.template,
                partial_variables={"format_instructions": self.parser.get_format_instructions()}
            )
    """)
    
    print("Benefits of this architecture:")
    print("✓ Centralized template management")
    print("✓ Type-safe template access")
    print("✓ Built-in validation")
    print("✓ Easy to extend with new templates")
    print("✓ Better maintainability")
    print("✓ Consistent prompt structure")


if __name__ == "__main__":
    demonstrate_template_usage()
    demonstrate_ai_engine_integration()
